import * as echarts from 'echarts'
import ynData from '@/assets/data/永宁县'
import mapBg from '@/assets/images/mapBg.png'
// 地图打点图标
import mapIcon1 from '@/assets/images/mapIcon1.png'
import mapIcon2 from '@/assets/images/mapIcon2.png'
import mapIcon3 from '@/assets/images/mapIcon3.png'
echarts.registerMap('yn', ynData as any)
const mapIconList: any = [mapIcon1, mapIcon2, mapIcon3]

// 获取地图配置
export const getMapOption = () => {
  // 渐变层颜色
  const colorList: string[] = [
    '#8b5e70',
    '#81596d',
    '#78556a',
    '#6e5068',
    '#644c65',
    '#5b4762',
    '#51435f',
    '#483e5c',
    '#3e3a59',
    '#343557',
    '#2b3154',
    '#212c51'
  ]
  // 生成渐变图层
  const geoList: any = []
  for (let i = 1; i <= colorList.length; i++) {
    const mapOption: any = {
      map: 'yn',
      aspectScale: 0.85,
      emphasis: {
        disabled: true
      },
      z: 12 - i,
      layoutCenter: ['50%', `${i * 0.3 + 50}%`], //地图位置
      layoutSize: '100%',
      itemStyle: {
        normal: {
          areaColor: colorList[i - 1],
          borderWidth: 0
        }
      }
    }
    if (i === colorList.length) {
      mapOption.itemStyle.normal.shadowColor = 'rgba(0, 0, 0, 0.71)'
      mapOption.itemStyle.normal.shadowBlur = 100
    }
    geoList.push(mapOption)
  }
  // 获取打点数据配置
  const pointSeriesData = getPointData()
  const option = {
    geo: [
      // 最外围发光边界
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'], //地图位置
        layoutSize: '100%',
        z: 12,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            borderColor: 'rgb(180, 137, 81)',
            borderWidth: 8,
            shadowColor: 'rgba(218, 163, 88, 0.4)',
            shadowBlur: 20
          }
        }
      },
      // 最外层遮罩蒙版
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'], //地图位置
        layoutSize: '100%',
        z: 14,
        itemStyle: {
          normal: {
            areaColor: 'rgba(106, 125, 171, 0.45)',
            borderWidth: 0
          }
        },
        label: {
          show: true,
          color: '#fff',
          fontSize: 14
        }
      },
      // 内部蓝色边界
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'], //地图位置
        layoutSize: '100%',
        z: 12,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            areaColor: {
              image: mapBg
            },
            borderColor: '#8aa5db',
            borderWidth: 1
          }
        }
      },
      ...geoList
    ],
    series: [
      // 地图打点数据
      ...pointSeriesData
    ]
  }
  return option
}

// 生成地图打点数据
const getPointData = () => {
  const districtData: {
    name: string
    value: number
    point: number[]
    type: number // 图标类型 0,1,2 对应三种不同图标
  }[] = [
    {
      name: '永宁县城',
      value: 267,
      point: [106.253781, 38.28043],
      type: 0
    },
    {
      name: '李俊镇',
      value: 200,
      point: [106.15, 38.35],
      type: 1
    },
    {
      name: '闽宁镇',
      value: 129,
      point: [106.35, 38.25],
      type: 2
    },
    {
      name: '望洪镇',
      value: 107,
      point: [106.20, 38.20],
      type: 0
    },
    {
      name: '杨和镇',
      value: 86,
      point: [106.30, 38.30],
      type: 1
    },
    {
      name: '胜利乡',
      value: 95,
      point: [106.18, 38.32],
      type: 2
    },
    {
      name: '望远镇',
      value: 78,
      point: [106.28, 38.18],
      type: 0
    },
    {
      name: '红寺堡镇',
      value: 156,
      point: [106.08, 38.28],
      type: 1
    }
  ]
  
  const pointSeriesData: any = []
  
  districtData.forEach((item: any, index: number) => {
    // 地图打点图标
    const mapPoint = {
      type: 'scatter',
      coordinateSystem: 'geo',
      geoIndex: 0,
      zlevel: 5,
      label: {
        show: false
      },
      symbol: `image://` + mapIconList[item.type],
      symbolSize: [40, 40],
      symbolOffset: [0, 0],
      z: 999,
      data: [
        {
          name: item.name,
          value: [item.point[0], item.point[1], item.value],
          itemStyle: {
            color: 'transparent'
          }
        }
      ],
      tooltip: {
        show: true,
        formatter: function (params: any) {
          return `${params.name}<br/>数值: ${params.value[2]}`
        }
      }
    }
    
    // 底部光圈效果
    const rippleEffect = {
      type: 'effectScatter',
      coordinateSystem: 'geo',
      geoIndex: 0,
      zlevel: 4,
      showEffectOn: 'render',
      rippleEffect: {
        scale: 3,
        brushType: 'stroke'
      },
      symbol: 'circle',
      symbolSize: [15, 15],
      itemStyle: {
        color: 'rgba(0, 255, 255, 0.8)',
        shadowBlur: 10,
        shadowColor: 'rgba(0, 255, 255, 0.5)'
      },
      data: [
        {
          name: item.name,
          value: [item.point[0], item.point[1], item.value]
        }
      ]
    }
    
    pointSeriesData.push(mapPoint)
    pointSeriesData.push(rippleEffect)
  })
  
  return pointSeriesData
}
